#!/usr/bin/env python3
"""Feedback Collector - Advanced Word Document Comment Analysis Tool

This enhanced version provides comprehensive support for extracting user feedback
from Word document comments and replies to Styling Agent suggestions. Features:

- Extracts all user replies to Styling Agent comments
- Supports multiple reply formats (t/f, yes/no, correct/incorrect, etc.)
- Handles multiple user replies per comment
- Categorizes feedback types (binary, approval, quality, etc.)
- Provides detailed analytics and export capabilities
- Supports both 'styling_agent' and 'Styling Agent' authors

Usage:
    python feedback_collector.py <document_path> [options]
"""

import argparse
import json
import re
import sys
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

try:
    import xml.etree.ElementTree as ET
    import zipfile
    from xml.etree.ElementTree import Element

    from docx import Document
except ImportError:
    print(
        "Error: Required library not installed. Please run: pip install python-docx lxml"
    )
    sys.exit(1)


@dataclass
class CommentReply:
    """Data class for comment replies"""

    reply_id: str
    author: str
    text: str
    date: Optional[str] = None


@dataclass
class CommentInfo:
    """Enhanced data class to store comment information with replies"""

    comment_id: str
    author: str
    text: str
    date: Optional[str] = None
    replies: Optional[List[CommentReply]] = None
    paragraph_text: str = ""
    user_feedback: Optional[str] = None
    is_correct: Optional[bool] = None
    para_id: str = ""
    parent_para_id: str | None = None
    comment_range_start: Optional[str] = None
    comment_range_end: Optional[str] = None

    def __post_init__(self):
        if self.replies is None:
            self.replies = []

    @property
    def feedback_detail_replies(self) -> list[CommentReply] | None:
        if self.replies is None:
            return None

        feedback_patterns = [
            r"^[tf]$",  # Just 't' or 'f'
            r"^(true|false)$",  # 'true' or 'false'
            r"^(correct|incorrect)$",  # 'correct' or 'incorrect'
            r"^(y|yes|no|n)$",  # 'yes' or 'no' or 'y' or 'n'
            r"^(ok|not ok)$",  # 'ok' or 'not ok'
            r"^(done|resolved)$",  # 'done' or 'resolved'
            r"^(good|bad)$",  # 'good' or 'bad'
            r"^(right|wrong)$",  # 'right' or 'wrong'
            r"^(approve|approved|reject|rejected)$",  # approval/rejection
            r"^(accept|accepted|decline|declined)$",  # acceptance/decline
        ]

        feedback_detail_comments = []
        for comment_reply in self.replies:
            # Check if this comment looks like feedback
            text = comment_reply.text.lower().strip()

            is_feedback = any(
                re.match(pattern, text, re.IGNORECASE) for pattern in feedback_patterns
            )

            if is_feedback:
                continue

            feedback_detail_comments.append(comment_reply)
        return feedback_detail_comments


class FeedbackCollector:
    """feedback collector for Word document comments with full reply support"""

    def __init__(self, document_path: str, filter_author: Optional[str] = None):
        self.document_path = Path(document_path)
        self.document = None
        self.comments = []
        self.comment_relationships = {}
        self.filter_author = filter_author  # Add author filter
        self.namespaces = {
            "w": "http://schemas.openxmlformats.org/wordprocessingml/2006/main",
            "w14": "http://schemas.microsoft.com/office/word/2010/wordml",
            "w15": "http://schemas.microsoft.com/office/word/2012/wordml",
        }

        if not self.document_path.exists():
            raise FileNotFoundError(f"Document not found: {document_path}")

        if not self.document_path.suffix.lower() == ".docx":
            raise ValueError("Only .docx files are supported")

    def load_document(self):
        """Load the Word document"""
        try:
            self.document = Document(str(self.document_path))
            print(f"Successfully loaded document: {self.document_path.name}")
        except Exception as e:
            raise Exception(f"Failed to load document: {str(e)}")

    def extract_all_comments(self) -> Dict[str, CommentInfo]:
        """Extract all comments including replies from XML structure"""
        comments_dict = {}

        try:
            with zipfile.ZipFile(self.document_path, "r") as docx_zip:
                # Extract main comments
                if "word/comments.xml" in docx_zip.namelist():
                    comments_dict.update(self._extract_main_comments(docx_zip))

                # Extract extended comments (replies)
                if "word/commentsExtended.xml" in docx_zip.namelist():
                    self._extract_comment_replies(docx_zip, comments_dict)

                # Extract comment ranges from document.xml
                if "word/document.xml" in docx_zip.namelist():
                    self._extract_comment_ranges(docx_zip, comments_dict)

        except Exception as e:
            print(f"Error extracting comments: {str(e)}")
            raise

        return comments_dict

    def _extract_main_comments(self, docx_zip) -> Dict[str, CommentInfo]:
        """Extract main comments from comments.xml and save paraId for each comment"""
        comments_dict = {}

        comments_xml = docx_zip.read("word/comments.xml")
        root = ET.fromstring(comments_xml)

        for comment in root.findall(".//w:comment", self.namespaces):
            comment_id = comment.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}id"
            )
            author = comment.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}author",
                "Unknown",
            )
            date = comment.get(
                "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}date"
            )

            para_id = ""
            w_p_elements = comment.findall(".//w:p", self.namespaces)
            if w_p_elements:
                # Get the last p element
                last_w_p_element = w_p_elements[-1]
                para_id = last_w_p_element.get(
                    "{http://schemas.microsoft.com/office/word/2010/wordml}paraId", ""
                )

            # Extract comment text
            comment_text = self._extract_text_from_element(comment)

            comment_info = CommentInfo(
                comment_id=comment_id or "unknown",
                author=author,
                text=comment_text.strip(),
                date=date,
                para_id=para_id,
            )

            comments_dict[comment_id or "unknown"] = comment_info

            print(
                f"Debug: Comment ID: {comment_id}, Author: {author}, ParaId: {para_id}"
            )

        return comments_dict

    def _extract_comment_replies(self, docx_zip, comments_dict: Dict[str, CommentInfo]):
        """Extract comment relationships from commentsExtended.xml using paraId"""
        extended_xml = docx_zip.read("word/commentsExtended.xml")
        root = ET.fromstring(extended_xml)

        print(f"Debug: Found commentsExtended.xml with root tag: {root.tag}")
        para_id_to_comment = {}

        for _, comment in comments_dict.items():
            para_id_to_comment[comment.para_id] = comment

        # Now check commentsExtended.xml for relationships
        for comment_ex in root.findall(".//w15:commentEx", self.namespaces):
            para_id = comment_ex.get(
                "{http://schemas.microsoft.com/office/word/2012/wordml}paraId"
            )
            parent_para_id = comment_ex.get(
                "{http://schemas.microsoft.com/office/word/2012/wordml}paraIdParent"
            )
            # done_status = comment_ex.get(
            #     "{http://schemas.microsoft.com/office/word/2012/wordml}done", "0"
            # )

            if parent_para_id:
                comment = para_id_to_comment[para_id]
                comment.parent_para_id = parent_para_id

        # Alternative approach: Look for comments that might be replies based on timing/content
        self._find_reply_comments(comments_dict)

    def _find_reply_comments(self, comments_dict: Dict[str, CommentInfo]):
        """Find comments that might be replies to styling_agent comments"""
        styling_comments = {}
        feedback_comments = {}
        for c in comments_dict.values():
            if self._is_styling_agent_comment(c):
                styling_comments[c.para_id] = c
            else:
                feedback_comments[c.para_id] = c

        for _, comment in feedback_comments.items():
            if comment.parent_para_id in styling_comments:
                styling_comment = styling_comments[comment.parent_para_id]
                self._add_reply_to_comment(styling_comment, comment)
                print(
                    f"Debug: Associated feedback '{comment.text}' with styling comment {styling_comment.comment_id} (sequential)"
                )

    def _add_reply_to_comment(
        self, styling_comment: CommentInfo, feedback_comment: CommentInfo
    ):
        """Add a reply to a styling comment and update feedback status"""
        reply = CommentReply(
            reply_id=feedback_comment.comment_id,
            author=feedback_comment.author,
            text=feedback_comment.text,
            date=feedback_comment.date,
        )

        if styling_comment.replies is None:
            styling_comment.replies = []
        styling_comment.replies.append(reply)

        # Update feedback status - prefer detailed feedback over simple annotation
        if not styling_comment.user_feedback or self._is_annotation_feedback(
            styling_comment.user_feedback
        ):
            styling_comment.user_feedback = feedback_comment.text.lower().strip()
            styling_comment.is_correct = self._evaluate_feedback(
                styling_comment.user_feedback
            )

    def _are_comment_ids_close(self, id1: str, id2: str) -> bool:
        """Check if two comment IDs are numerically close (within 10 units)"""
        try:
            num1 = int(id1)
            num2 = int(id2)
            return abs(num1 - num2) <= 10
        except (ValueError, TypeError):
            return False

    def _extract_comment_ranges(self, docx_zip, comments_dict: Dict[str, CommentInfo]):
        """Extract comment range information from document.xml"""
        try:
            doc_xml = docx_zip.read("word/document.xml")
            root = ET.fromstring(doc_xml)

            # Find comment range starts and ends
            for comment_start in root.findall(
                ".//w:commentRangeStart", self.namespaces
            ):
                comment_id = comment_start.get(
                    "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}id"
                )
                if comment_id in comments_dict:
                    comments_dict[comment_id].comment_range_start = comment_id

                    # Try to find associated paragraph text
                    paragraph = self._find_parent_paragraph(comment_start)
                    if paragraph:
                        para_text = self._extract_text_from_element(paragraph)
                        comments_dict[comment_id].paragraph_text = para_text.strip()

        except Exception as e:
            print(f"Warning: Could not extract comment ranges: {str(e)}")

    def _extract_text_from_element(self, element: Element) -> str:
        """Extract text content from XML element"""
        text_parts = []

        # Handle different text elements
        for t_elem in element.iter():
            if t_elem.tag.endswith("}t") and t_elem.text:
                text_parts.append(t_elem.text)

        return "".join(text_parts)

    def _find_parent_paragraph(self, element: Element) -> Optional[Element]:
        """Find the parent paragraph element - simplified version"""
        # For now, return None to avoid getparent() issues
        # This is a simplified implementation that skips paragraph context
        _ = element  # Mark parameter as used
        return None

    def analyze_comments(self):
        """Analyze all comments in the document"""
        if not self.document:
            self.load_document()

        # Extract all comments with replies
        comments_dict = self.extract_all_comments()

        # Process comments to identify AI comments and user feedback
        self.comments = self._process_comments(comments_dict)

        print(f"Found {len(self.comments)} comments to analyze")
        total_replies = sum(len(comment.replies or []) for comment in self.comments)
        print(f"Found {total_replies} total replies")

        # Debug: Show comment details if verbose mode or filter_author is set
        if self.filter_author:
            print(f"\nComments from author '{self.filter_author}':")
            for i, comment in enumerate(self.comments, 1):
                print(f"  {i}. ID: {comment.comment_id}, Text: {comment.text[:100]}...")
                if comment.replies:
                    print(f"     Replies ({len(comment.replies)}):")
                    for reply in comment.replies:
                        print(f"       - {reply.author}: {reply.text}")
                else:
                    print("     No replies found")

    def _process_comments(
        self, comments_dict: Dict[str, CommentInfo]
    ) -> List[CommentInfo]:
        """Process comments to identify AI comments and user feedback"""
        processed_comments = []

        for comment in comments_dict.values():
            # Check if comment matches the filter criteria
            should_include = False

            if self.filter_author:
                # If filter_author is specified, only include comments from that author
                should_include = comment.author == self.filter_author
            else:
                # Default behavior: check if this is a styling agent comment
                should_include = self._is_styling_agent_comment(comment)

            if should_include:
                # Look for user feedback in replies
                user_feedback = self._find_user_feedback_in_replies(comment)

                if user_feedback:
                    comment.user_feedback = user_feedback.lower().strip()
                    comment.is_correct = self._evaluate_feedback(comment.user_feedback)

                processed_comments.append(comment)

        return processed_comments

    def _is_styling_agent_comment(self, comment: CommentInfo) -> bool:
        """Determine if a comment is from styling_agent specifically"""
        return comment.author in ["styling_agent", "Styling Agent"]

    def _find_user_feedback_in_replies(self, comment: CommentInfo) -> Optional[str]:
        """Find user feedback in comment replies - returns first valid feedback found"""
        feedback_patterns = [
            r"^[tf]$",  # Just 't' or 'f'
            r"^(true|false)$",  # 'true' or 'false'
            r"^(correct|incorrect)$",  # 'correct' or 'incorrect'
            r"^(yes|no)$",  # 'yes' or 'no'
            r"^(ok|not ok)$",  # 'ok' or 'not ok'
        ]

        # Check all replies for feedback
        for reply in comment.replies or []:
            reply_text = reply.text.lower().strip()

            for pattern in feedback_patterns:
                if re.match(pattern, reply_text, re.IGNORECASE):
                    return reply_text

        return None

    def _is_annotation_feedback(self, feedback_text: str) -> bool:
        """Check if the feedback is just annotation (t/f, true/false, etc.) rather than detailed reply"""
        text = feedback_text.lower().strip()

        # Define patterns for annotation feedback (simple binary responses)
        annotation_patterns = [
            r"^[tf]$",  # Just 't' or 'f'
            r"^(true|false)$",  # 'true' or 'false'
            r"^(correct|incorrect)$",  # 'correct' or 'incorrect'
            r"^(yes|no)$",  # 'yes' or 'no'
            r"^(ok|not ok)$",  # 'ok' or 'not ok'
            r"^(done|resolved)$",  # 'done' or 'resolved'
            r"^(good|bad)$",  # 'good' or 'bad'
            r"^(right|wrong)$",  # 'right' or 'wrong'
            r"^(approve|approved|reject|rejected)$",  # approval/rejection
            r"^(accept|accepted|decline|declined)$",  # acceptance/decline
            r"^\d+$",  # Just numbers (could be rating)
        ]

        # Check if text matches any annotation pattern
        for pattern in annotation_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True

        # Consider very short responses (<=5 characters) as likely annotations
        # BUT only if they look like simple responses, not detailed feedback
        if len(text) <= 5 and not any(
            char.isalpha()
            for char in text
            if char not in ["t", "f", "y", "n", "o", "k"]
        ):
            return True

        return False

    def _evaluate_feedback(self, feedback: str) -> Optional[bool]:
        """Evaluate if feedback indicates correctness"""
        if not feedback:
            return None

        positive_feedback = [
            "t",
            "true",
            "correct",
            "yes",
            "ok",
            "good",
            "right",
            "approve",
            "approved",
            "accept",
            "accepted",
            "done",
            "resolved",
            "y",
        ]
        negative_feedback = [
            "f",
            "false",
            "incorrect",
            "no",
            "not ok",
            "bad",
            "wrong",
            "reject",
            "rejected",
            "decline",
            "declined",
        ]

        clean_feedback = feedback.lower().strip()

        # Check for positive feedback
        if clean_feedback in positive_feedback:
            return True

        # Check for negative feedback
        if clean_feedback in negative_feedback:
            return False

        # Handle numeric feedback (assume >5 is positive for 1-10 scale)
        if clean_feedback.isdigit():
            try:
                score = int(clean_feedback)
                if score >= 5:  # Assume 5+ out of 10 is positive
                    return True
                else:
                    return False
            except ValueError:
                pass

        # Default to None for ambiguous feedback
        return None

    def generate_detailed_report(self) -> Dict:
        """Generate comprehensive analysis report"""
        total_comments = len(self.comments)
        comments_with_feedback = sum(
            1 for c in self.comments if c.user_feedback is not None
        )
        correct_modifications = sum(1 for c in self.comments if c.is_correct is True)
        incorrect_modifications = sum(1 for c in self.comments if c.is_correct is False)
        comments_without_feedback = total_comments - comments_with_feedback

        # Strategy 3: Separate statistics for different scenarios
        feedback_accuracy = (
            (correct_modifications / comments_with_feedback * 100)
            if comments_with_feedback > 0
            else 0
        )

        # Overall accuracy (no feedback = incorrect)
        overall_accuracy = (
            (correct_modifications / total_comments * 100) if total_comments > 0 else 0
        )

        # Optimistic accuracy (no feedback = correct)
        optimistic_accuracy = (
            ((correct_modifications + comments_without_feedback) / total_comments * 100)
            if total_comments > 0
            else 0
        )

        # Collect detailed statistics
        author_stats = defaultdict(lambda: {"total": 0, "correct": 0, "incorrect": 0})
        for comment in self.comments:
            author_stats[comment.author]["total"] += 1
            if comment.is_correct is True:
                author_stats[comment.author]["correct"] += 1
            elif comment.is_correct is False:
                author_stats[comment.author]["incorrect"] += 1

        # Collect error details with reply information
        error_details = []
        for comment in self.comments:
            if comment.is_correct is False:
                error_detail = {
                    "comment_id": comment.comment_id,
                    "author": comment.author,
                    "comment_text": comment.text,
                    "paragraph_text": comment.paragraph_text,
                    "user_feedback": comment.user_feedback,
                    "position_info": comment.para_id,
                    "replies": [asdict(reply) for reply in comment.replies or []],
                }
                error_details.append(error_detail)

        # Extract all user replies for detailed analysis
        all_user_replies = self._extract_all_user_replies()

        # Count replies statistics - distinguish between annotation feedback and detailed replies
        total_replies = sum(len(comment.replies or []) for comment in self.comments)
        comments_with_replies = sum(
            1 for c in self.comments if c.replies and len(c.replies) > 0
        )

        # Count annotation feedback vs detailed replies
        annotation_feedback_count = 0
        detailed_replies_count = 0

        for comment in self.comments:
            if comment.replies:
                for reply in comment.replies:
                    if self._is_annotation_feedback(reply.text):
                        annotation_feedback_count += 1
                    else:
                        detailed_replies_count += 1

        return {
            "summary": {
                "total_comments": total_comments,
                "comments_with_feedback": comments_with_feedback,
                "correct_modifications": correct_modifications,
                "incorrect_modifications": incorrect_modifications,
                "feedback_accuracy": feedback_accuracy,
                "overall_accuracy": overall_accuracy,
                "optimistic_accuracy": optimistic_accuracy,
                "comments_without_feedback": comments_without_feedback,
                "total_replies": total_replies,
                "comments_with_replies": comments_with_replies,
                "annotation_feedback_count": annotation_feedback_count,
                "detailed_replies_count": detailed_replies_count,
            },
            "author_statistics": dict(author_stats),
            "error_details": error_details,
            "all_comments": [asdict(comment) for comment in self.comments],
            "all_user_replies": all_user_replies,
        }

    def _extract_all_user_replies(self) -> List[Dict]:
        """Extract all user replies across all styling agent comments, excluding True/False values"""
        all_replies = []

        for comment in self.comments:
            if not comment.replies:
                continue
            for reply in comment.replies:
                # Skip True/False annotation feedback
                if self._is_annotation_feedback(reply.text):
                    continue
                reply_info = {
                    "original_comment_id": comment.comment_id,
                    "original_comment_author": comment.author,
                    "original_comment_text": (
                        comment.text[:100] + "..."
                        if len(comment.text) > 100
                        else comment.text
                    ),
                    "reply_id": reply.reply_id,
                    "reply_author": reply.author,
                    "reply_text": reply.text,
                    "reply_date": reply.date,
                    "is_positive_feedback": self._evaluate_feedback(reply.text),
                    "feedback_category": self._categorize_feedback(reply.text),
                    "is_annotation_feedback": False,  # All remaining replies are non-annotation
                }
                all_replies.append(reply_info)

        return all_replies

    def _categorize_feedback(self, feedback_text: str) -> str:
        """Categorize the type of feedback"""
        text = feedback_text.lower().strip()

        # Binary feedback
        if re.match(r"^[tf]$", text):
            return "binary_tf"
        elif re.match(r"^(true|false)$", text):
            return "binary_true_false"
        elif re.match(r"^(yes|no)$", text):
            return "binary_yes_no"
        elif re.match(r"^(correct|incorrect)$", text):
            return "binary_correct_incorrect"

        # Approval feedback
        elif re.match(r"^(approve|approved|reject|rejected)$", text):
            return "approval"
        elif re.match(r"^(accept|accepted|decline|declined)$", text):
            return "acceptance"

        # Quality feedback
        elif re.match(r"^(good|bad)$", text):
            return "quality"
        elif re.match(r"^(right|wrong)$", text):
            return "correctness"
        elif re.match(r"^(ok|not ok)$", text):
            return "status"

        # Completion feedback
        elif re.match(r"^(done|resolved)$", text):
            return "completion"

        # Numeric feedback
        elif text.isdigit():
            return "numeric_rating"

        # Long form feedback
        elif len(text) > 20:
            return "detailed_feedback"
        else:
            return "other"

    def print_enhanced_report(self, report: Dict):
        """Print detailed analysis report"""
        print("=" * 70)
        print(f"Document: {self.document_path.name}")
        print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        summary = report["summary"]
        print("\n" + "-" * 50)
        print("SUMMARY STATISTICS")
        print("-" * 50)
        print(f"Total AI Comments: {summary['total_comments']}")
        print(f"Comments with User Feedback: {summary['comments_with_feedback']}")
        print(f"Comments without Feedback: {summary['comments_without_feedback']}")
        print(f"Correct Modifications: {summary['correct_modifications']}")
        print(f"Incorrect Modifications: {summary['incorrect_modifications']}")
        print(f"\nACCURACY ANALYSIS:")
        print(
            f"  Feedback-Only Accuracy: {summary['feedback_accuracy']:.2f}% (based on {summary['comments_with_feedback']} comments with feedback)"
        )
        print(
            f"  Overall Accuracy (conservative): {summary['overall_accuracy']:.2f}% (no feedback = incorrect)"
        )
        print(
            f"  Optimistic Accuracy: {summary['optimistic_accuracy']:.2f}% (no feedback = correct)"
        )
        print(f"Total User Replies: {summary['total_replies']}")
        print(f"Comments with Replies: {summary['comments_with_replies']}")
        print(
            f"  - Annotation Feedback (T/F, etc.): {summary['annotation_feedback_count']}"
        )
        print(f"  - Detailed User Replies: {summary['detailed_replies_count']}")

        # Author statistics
        if report["author_statistics"]:
            print("\n" + "-" * 50)
            print("AUTHOR STATISTICS")
            print("-" * 50)
            for author, stats in report["author_statistics"].items():
                total = stats["total"]
                correct = stats["correct"]
                incorrect = stats["incorrect"]
                author_accuracy = (
                    (correct / (correct + incorrect) * 100)
                    if (correct + incorrect) > 0
                    else 0
                )
                print(f"{author}:")
                print(f"  Total Comments: {total}")
                print(f"  Correct: {correct}, Incorrect: {incorrect}")
                print(f"  Accuracy: {author_accuracy:.2f}%")

        # User replies section (excluding True/False values)
        if report["all_user_replies"]:
            print("\n" + "-" * 50)
            print("USER REPLIES (excluding True/False)")
            print("-" * 50)

            user_replies = report["all_user_replies"]
            print(f"\nUSER REPLIES ({len(user_replies)} replies):")
            print("Detailed responses from users (True/False values excluded):")

            # Group replies by original comment
            from collections import defaultdict

            replies_by_comment = defaultdict(list)
            for reply in user_replies:
                replies_by_comment[reply["original_comment_id"]].append(reply)

            for comment_id, replies in replies_by_comment.items():
                if len(replies) == 1:
                    reply = replies[0]
                    feedback_status = (
                        "✓"
                        if reply["is_positive_feedback"]
                        else "✗" if reply["is_positive_feedback"] is False else "?"
                    )
                    print(
                        f"  {feedback_status} {reply['reply_author']}: '{reply['reply_text']}'"
                    )
                    print(f"     → Comment ID: {comment_id}")
                else:
                    # Multiple replies for same comment - join with '|'
                    reply_texts = [r["reply_text"] for r in replies]
                    authors = [r["reply_author"] for r in replies]
                    combined_text = " | ".join(reply_texts)
                    combined_authors = " | ".join(authors)
                    print(f"  ? {combined_authors}: '{combined_text}'")
                    print(f"     → Comment ID: {comment_id}")
                    print(f"     → Multiple replies ({len(replies)} total)")

        # Error details
        if report["error_details"]:
            print("\n" + "-" * 50)
            print("INCORRECT MODIFICATIONS DETAILS")
            print("-" * 50)
            for i, error in enumerate(report["error_details"], 1):
                print(f"\n{i}. Comment ID: {error['comment_id']}")
                print(f"   Author: {error['author']}")
                print(f"   Comment: {error['comment_text'][:150]}...")
                print(f"   User Feedback: {error['user_feedback']}")
                if error["paragraph_text"]:
                    print(f"   Context: {error['paragraph_text'][:150]}...")
                if error["replies"]:
                    print(f"   Replies ({len(error['replies'])}):")
                    for reply in error["replies"]:
                        print(f"     - {reply['author']}: {reply['text']}")

        print("\n" + "=" * 70)

    def export_to_excel(self, report: Dict, output_file: str):
        """Export analysis results to Excel format with multiple sheets"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font
        except ImportError:
            print("Warning: openpyxl not installed. Cannot export to Excel.")
            return

        wb = Workbook()

        # Summary sheet
        ws_summary = wb.active
        if ws_summary is not None:
            ws_summary.title = "Summary"

            # Add headers
            ws_summary["A1"] = "Metric"
            ws_summary["B1"] = "Value"
            ws_summary["A1"].font = Font(bold=True)
            ws_summary["B1"].font = Font(bold=True)

            # Add summary data
            summary = report["summary"]
            row = 2
            for key, value in summary.items():
                display_key = key.replace("_", " ").title()
                # Special formatting for specific keys
                if key == "annotation_feedback_count":
                    display_key = "Annotation Feedback (T/F, etc.)"
                elif key == "detailed_replies_count":
                    display_key = "Detailed User Replies"

                ws_summary[f"A{row}"] = display_key
                ws_summary[f"B{row}"] = (
                    f"{value:.2f}%"
                    if key
                    in ["feedback_accuracy", "overall_accuracy", "optimistic_accuracy"]
                    else value
                )
                row += 1

            # Detailed comments sheet
            ws_details = wb.create_sheet("Detailed Comments")
            headers = [
                "Comment ID",
                "Author",
                "Comment Text",
                "User Feedback",
                "Is Correct",
                "Replies Count",
                "Context",
            ]
            for col, header in enumerate(headers, 1):
                ws_details.cell(row=1, column=col, value=header).font = Font(bold=True)

            for row, comment_data in enumerate(report["all_comments"], 2):
                feedback_replies = []
                for reply in comment_data["replies"]:
                    feedback_replies.append(reply["text"])

                feedback_text = " | ".join(feedback_replies)
                ws_details.cell(row=row, column=1, value=comment_data["comment_id"])
                ws_details.cell(row=row, column=2, value=comment_data["author"])
                ws_details.cell(
                    row=row,
                    column=3,
                    value=feedback_text,
                )
                ws_details.cell(
                    row=row,
                    column=4,
                    value=comment_data["user_feedback"] or "No feedback",
                )
                ws_details.cell(
                    row=row,
                    column=5,
                    value=(
                        "Yes"
                        if comment_data["is_correct"] is True
                        else "No" if comment_data["is_correct"] is False else "Unknown"
                    ),
                )
                ws_details.cell(row=row, column=6, value=len(comment_data["replies"]))
                ws_details.cell(
                    row=row,
                    column=7,
                    value=(
                        comment_data["paragraph_text"][:100] + "..."
                        if len(comment_data["paragraph_text"]) > 100
                        else comment_data["paragraph_text"]
                    ),
                )

        wb.save(output_file)
        print(f"Results exported to Excel: {output_file}")


def main():
    """Main function to run the advanced analysis"""
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python feedback_collector.py document.docx                    # Extract all user replies to Styling Agent comments
    python feedback_collector.py document.docx --author user123   # Filter specific author comments
    python feedback_collector.py document.docx --output-format json --output-file results.json
    python feedback_collector.py document.docx --output-format excel --output-file results.xlsx
    python feedback_collector.py document.docx --verbose          # Show detailed debug information
    
Features:
    - Extracts multiple user replies per Styling Agent comment
    - Recognizes various feedback formats: t/f, yes/no, correct/incorrect, approve/reject, etc.
    - Categorizes feedback types and provides detailed analytics
    - Exports results to JSON or Excel with separate sheets for comments and replies
        """,
    )

    parser.add_argument("document_path", help="Path to the Word document (.docx)")
    parser.add_argument(
        "--output-format", choices=["json", "excel"], help="Export format for results"
    )
    parser.add_argument("--output-file", help="Output file name for export")
    parser.add_argument(
        "--author",
        default=None,
        help="Filter comments by specific author (default: both 'styling_agent' and 'Styling Agent')",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose output"
    )

    args = parser.parse_args()

    try:
        # Initialize analyzer
        print("Initializing Advanced Word Comment Analyzer...")
        analyzer = FeedbackCollector(args.document_path, args.author)

        # Load and analyze document
        print("Loading document...")
        analyzer.load_document()

        if args.author:
            print(
                f"Analyzing comments and replies (filtering by author: {args.author})..."
            )
        else:
            print(
                "Analyzing comments and replies (default: styling_agent and Styling Agent)..."
            )
        analyzer.analyze_comments()

        # Generate results
        print("Generating detailed analysis results...")
        report = analyzer.generate_detailed_report()

        # Print report
        analyzer.print_enhanced_report(report)

        # Export if requested
        if args.output_format and args.output_file:
            if args.output_format == "json":
                with open(args.output_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                print(f"Results exported to JSON: {args.output_file}")
            elif args.output_format == "excel":
                analyzer.export_to_excel(report, args.output_file)
        elif args.output_format:
            # Generate default filename
            base_name = Path(args.document_path).stem
            if args.output_format == "json":
                default_file = f"{base_name}_analysis.json"
                with open(default_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                print(f"Results exported to JSON: {default_file}")
            elif args.output_format == "excel":
                default_file = f"{base_name}_analysis.xlsx"
                analyzer.export_to_excel(report, default_file)

    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        if args.verbose:
            import traceback

            traceback.print_exc()
        sys.exit(1)


def test_annotation_feedback():
    """Test function to verify annotation feedback detection"""
    collector = FeedbackCollector.__new__(
        FeedbackCollector
    )  # Create instance without __init__

    # Test cases for annotation feedback
    annotation_cases = [
        "t",
        "f",
        "T",
        "F",
        "true",
        "false",
        "TRUE",
        "FALSE",
        "yes",
        "no",
        "YES",
        "NO",
        "correct",
        "incorrect",
        "ok",
        "good",
        "bad",
        "1",
        "5",
        "10",
    ]

    # Test cases for detailed replies
    detailed_cases = [
        "This looks good but needs some adjustment",
        "I think this could be improved by adding more details",
        "The formatting is correct but the content needs work",
        "Please revise this section according to our guidelines",
    ]

    print("Testing annotation feedback detection:")
    print("\nAnnotation feedback cases:")
    for case in annotation_cases:
        result = collector._is_annotation_feedback(case)
        print(f"  '{case}' -> {result}")

    print("\nDetailed reply cases:")
    for case in detailed_cases:
        result = collector._is_annotation_feedback(case)
        print(f"  '{case[:30]}...' -> {result}")


if __name__ == "__main__":
    # Uncomment the line below to run tests
    # test_annotation_feedback()
    main()
